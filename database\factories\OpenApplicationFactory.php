<?php

namespace Database\Factories;

use App\Models\OpenApplication;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OpenApplication>
 */
class OpenApplicationFactory extends Factory
{
    protected $model = OpenApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'course_code' => $this->faker->numberBetween(1, 50),
            'display_name' => $this->faker->sentence(4),
            'reg_year' => $this->faker->year(),
            'batch' => $this->faker->numberBetween(1, 4),
            'opening_date' => $this->faker->date(),
            'closing_date' => $this->faker->dateTimeBetween('now', '+6 months')->format('Y-m-d'),
            'active_status' => 1,
            'application_status' => $this->faker->numberBetween(85, 86),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the application is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'active_status' => 1,
        ]);
    }

    /**
     * Indicate that the application is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'active_status' => 0,
        ]);
    }
}
