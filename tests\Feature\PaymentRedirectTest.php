<?php

namespace Tests\Feature;

use App\Models\Applicant;
use App\Models\OpenApplication;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentRedirectTest extends TestCase
{
    use RefreshDatabase;

    public function test_payment_status_api_returns_completed_status()
    {
        // Create test data
        $openApplication = OpenApplication::factory()->create();
        $applicant = Applicant::factory()->create([
            'open_application_id' => $openApplication->id,
            'student_payment_id' => 12345,
            'submit_status' => 3, // Payment completed
            'application_fee_payment_status' => 1,
            'payment_method' => 2,
            'paid_amount' => 5000.00,
        ]);

        // Test the API endpoint
        $response = $this->postJson('/api/payment/status', [
            'student_payment_id' => 12345
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'student_payment_id' => 12345,
                        'submit_status' => 3,
                        'application_fee_payment_status' => 1,
                    ]
                ]);

        // Verify the data indicates payment completion
        $data = $response->json('data');
        $this->assertEquals(3, $data['submit_status']);
        $this->assertEquals(1, $data['application_fee_payment_status']);
    }

    public function test_application_final_route_accessible_after_payment()
    {
        // Set up session to simulate OTP verification
        session(['otp_verified' => true]);

        // Create test data
        $openApplication = OpenApplication::factory()->create();
        $applicant = Applicant::factory()->create([
            'open_application_id' => $openApplication->id,
            'submit_status' => 3, // Payment completed
            'application_fee_payment_status' => 1,
        ]);

        // Test accessing the final application route
        $response = $this->get(route('application.final', [
            encrypt($openApplication->id),
            encrypt($applicant->id)
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('frontend.application.final');
        $response->assertViewHas(['courseData', 'application', 'CourseOpenId', 'applicationId']);
    }

    public function test_application_final_route_redirects_if_not_submitted()
    {
        // Set up session to simulate OTP verification
        session(['otp_verified' => true]);

        // Create test data with unsubmitted application
        $openApplication = OpenApplication::factory()->create();
        $applicant = Applicant::factory()->create([
            'open_application_id' => $openApplication->id,
            'submit_status' => 1, // Not submitted
        ]);

        // Test accessing the final application route
        $response = $this->get(route('application.final', [
            encrypt($openApplication->id),
            encrypt($applicant->id)
        ]));

        // Should redirect back to application show page
        $response->assertRedirect();
        $response->assertSessionHas('alert-type', 'error');
    }
}
