<?php $__env->startSection('frontend'); ?>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    

    <!-- Header Section -->
    <section class="content-header mb-2">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h3 class="text-center"><?php echo e($courseData->display_name); ?></h3>
                </div>
            </div>
        </div>
         <?php if(isset($courseFees->amount) && $courseFees->amount > 0 && $application->student_payment_id != 0): ?>
         <?php else: ?>
         <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i> Please contact FGS for assistance with your payment.
        </div>
         <?php endif; ?>
    </section>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="application-wrapper">
                    <div class="card">
                        
                        <div class="card-body">
                            <!-- Application Summary -->
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <div class="card" style="border-color: #990000;">
                                        <div class="card-header text-white" style="background-color: #990000;">
                                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Application Summary</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Applicant Name:</strong> <?php echo e($application->getTitleName->category_name ?? ''); ?> <?php echo e($application->name_denoted_by_initials); ?> <?php echo e($application->last_name); ?></p>
                                                    <p><strong>Reference No:</strong> <?php echo e($application->reference_no); ?></p>
                                                    <p><strong>Status:</strong> <span id="applicationStatus"><?php if($application->submit_status == 1): ?>
                                            <span class="badge badge-pill badge-danger">Application Draft</span>
                                            <?php elseif($application->submit_status == 2): ?>
                                            <span class="badge badge-pill badge-warning">Application Payment Pending</span>
                                            <?php elseif($application->submit_status == 3): ?>
                                            <span class="badge badge-pill badge-success">Application Completed with payment</span>
                                            <?php endif; ?></span></p>


                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Email:</strong> <?php echo e($application->email); ?></p>
                                                    <p><strong>Mobile:</strong> <?php echo e($application->mobile_no); ?></p>
                                                    <p><strong>Submission Date:</strong> <?php echo e($application->basic_data_submit_date ? date('d/m/Y', strtotime($application->basic_data_submit_date)) : 'N/A'); ?></p>
                                                </div>
                                            </div>

                                            <!-- Payment Status Indicator -->
                                            <div id="paymentStatusIndicator" class="alert alert-info" style="display: none;">
                                                <i class="fas fa-sync fa-spin"></i>
                                                <strong>Checking Payment Status...</strong>
                                                <span>We're monitoring your payment in the background. Complete your payment in the popup window and we'll automatically detect when it's finished.</span>
                                                <br><small class="text-muted">You can close this page and return later - your payment status will be saved.</small>
                                            </div>



                                            <!-- Download PDF Button -->
                                            <div class="mt-3 text-center">
                                                <hr style="border-color: #990000; margin: 15px 0;">
                                                <p class="mb-2" style="font-size: 12px; color: #666;">
                                                    <i class="fas fa-info-circle"></i> Download your complete application summary for your records
                                                </p>
                                                <a href="<?php echo e(route('application.download.pdf', ['openAppID' => encrypt($CourseOpenId), 'id' => encrypt($applicationId)])); ?>"
                                                   class="btn btn-outline-danger btn-sm"
                                                   target="_blank"
                                                   title="Download complete application summary as PDF">
                                                    <i class="fas fa-file-pdf"></i> Download Application PDF
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card" style="border-color: #990000;">
                                        <div class="card-header text-white" style="background-color: #990000;">
                                            <h5 class="mb-0"><i class="fas fa-dollar-sign"></i> Application Payment Details</h5>
                                        </div>
                                        <div class="card-body text-center">
                                            <?php if(isset($courseFees) && $courseFees->count() > 0): ?>

                                                <h3 class="text-success">LKR <?php echo e(number_format($courseFees->amount, 2)); ?></h3>
                                                <p class="mb-0">Application Fees</p>
                                            <?php else: ?>
                                                <h3 class="text-warning">Application Fee Not Set</h3>
                                                <p class="mb-0">Please contact FGS</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- Payment Instructions -->
                            <div class="card mb-4" style="border-color: #990000;">
                                <div class="card-header text-white" style="background-color: #990000;">
                                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Important Payment Instructions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-dark">
                                        <h6><i class="fas fa-info-circle"></i> Please read carefully before proceeding with payment:</h6>
                                        <ul class="mb-0">
                                            <li>Payment must be completed within <strong>7 days</strong> of application submission</li>
                                            <li>Keep your payment receipt/transaction ID for future reference</li>
                                            <li>Payment confirmation may take 1-2 business days for bank deposits</li>
                                            <li>For any payment issues, contact our support team with your reference number</li>
                                            <li>Refunds are subject to university policy and terms & conditions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Options -->
                            <div class="card" style="border-color: #990000;">
                                <div class="card-header text-white" style="background-color: #990000;">
                                    <h5 class="mb-0"><i class="fas fa-payment"></i> Choose Your Payment Method</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Bank Deposit Option -->

                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-option" data-payment="bank">
                                                <div class="card-body text-center">
                                                    <div class="payment-icon mb-3">
                                                        <i class="fas fa-university fa-3x" style="color: #990000;"></i>
                                                    </div>
                                                    <h5 class="card-title">Bank Deposit</h5>
                                                    <p class="card-text">Transfer funds directly to our bank account</p>
                                                    <ul class="list-unstyled text-left">
                                                        <li><i class="fas fa-check text-success"></i> Secure bank transfer</li>
                                                        <li><i class="fas fa-check text-success"></i> 1-2 business days processing</li>
                                                        <li><i class="fas fa-check text-success"></i> No additional fees</li>
                                                    </ul>
                                                </div>
                                                <?php if(isset($courseFees->amount) && $courseFees->amount > 0 && $application->student_payment_id != 0): ?>
                                                <form id="bankPaymentForm" action="<?php echo e(route('bank.payment.download')); ?>" method="POST" target="_blank">
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="payment_course_code" value="<?php echo e($courseData->payment_course_code); ?>">
                                                <input type="hidden" name="nic" value="<?php echo e($application->active_nic == 1 ? $application->old_nic : ($application->active_nic == 2 ? $application->new_nic : ($application->passport ?? null))); ?>">
                                                <input type="hidden" name="amu" value="<?php echo e($courseFees->amount ?? 0); ?>">
                                                <input type="hidden" name="email" value="<?php echo e($application->email); ?>">
                                                <input type="hidden" name="mobile" value="<?php echo e($application->mobile_no); ?>">
                                                <div class="card-footer">
                                                     <button class="btn btn-block payment-btn" data-payment="bank" style="background-color: #990000; border-color: #990000; color: white;" type="submit">
                                                        <i class="fas fa-university"></i> Pay via Bank Deposit
                                                    </button>
                                                </div>
                                                </form>
                                                <?php else: ?>
                                                <div class="alert alert-warning" role="alert">
                                                    <i class="fas fa-exclamation-triangle"></i> Please contact FGS for assistance with your payment.
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>


                                        <!-- Credit Card Option -->
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-option" data-payment="card">
                                                <div class="card-body text-center">
                                                    <div class="payment-icon mb-3">
                                                        <i class="fas fa-credit-card fa-3x" style="color: #990000;"></i>
                                                    </div>
                                                    <h5 class="card-title">Credit Card</h5>
                                                    <p class="card-text">Pay instantly with your credit/debit card</p>
                                                    <ul class="list-unstyled text-left">
                                                        <li><i class="fas fa-check text-success"></i> Instant payment</li>
                                                        <li><i class="fas fa-check text-success"></i> Secure SSL encryption</li>
                                                        <li><i class="fas fa-check text-success"></i> Visa, MasterCard accepted</li>
                                                    </ul>
                                                </div>
                                               <?php if(isset($courseFees->amount) && $courseFees->amount > 0 && $application->student_payment_id != 0): ?>
                                                <form id="cardPaymentForm">
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="payment_course_code" value="<?php echo e($courseData->payment_course_code); ?>">
                                                <input type="hidden" name="nic" value="<?php echo e($application->active_nic == 1 ? $application->old_nic : ($application->active_nic == 2 ? $application->new_nic : ($application->passport ?? null))); ?>">
                                                <input type="hidden" name="amu" value="<?php echo e($courseFees->amount ?? 0); ?>">
                                                <input type="hidden" name="email" value="<?php echo e($application->email); ?>">
                                                <input type="hidden" name="mobile" value="<?php echo e($application->mobile_no); ?>">

                                                <div class="card-footer">
                                                    <button class="btn btn-block payment-btn" data-payment="card" style="background-color: #990000; border-color: #990000; color: white;" type="submit">
                                                        <i class="fas fa-credit-card"></i> Pay with Card
                                                    </button>
                                                </div>
                                            </form>
                                            <?php else: ?>
                                           <div class="alert alert-warning" role="alert">
                                                    <i class="fas fa-exclamation-triangle"></i> Please contact FGS for assistance with your payment.
                                                </div>
                                            <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- QR Payment Option (Disabled) -->
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-option-disabled" style="opacity: 0.6; cursor: not-allowed;">
                                                <div class="card-body text-center">
                                                    <div class="payment-icon mb-3">
                                                        <i class="fas fa-qrcode fa-3x text-secondary"></i>
                                                    </div>
                                                    <h5 class="card-title text-secondary">QR Payment</h5>
                                                    <p class="card-text text-muted">Scan QR code with your mobile banking app</p>
                                                    <ul class="list-unstyled text-left">
                                                        <li><i class="fas fa-times text-muted"></i> Currently unavailable</li>
                                                        <li><i class="fas fa-times text-muted"></i> Coming soon</li>
                                                        <li><i class="fas fa-times text-muted"></i> Use other payment methods</li>
                                                    </ul>
                                                </div>
                                                <div class="card-footer">
                                                    <button class="btn btn-block btn-secondary" disabled>
                                                        <i class="fas fa-qrcode"></i> QR Payment
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Support Information -->
                            <div class="card mt-4" style="border-color: #990000;">
                                <div class="card-header text-white" style="background-color: #990000;">
                                    <h6 class="mb-0"><i class="fas fa-headset"></i> Need Help?</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Email Support:</strong> <EMAIL></p>
                                            <p><strong>Phone Support:</strong> +94 11 2758759</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Office Hours:</strong> Monday - Friday, 9:00 AM - 5:00 PM</p>
                                            <p><strong>Reference Number:</strong> <?php echo e($application->reference_no); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Modals will be added here -->

    <style>
        :root {
            --primary-color: #990000;
            --primary-hover: #770000;
            --primary-light: rgba(153, 0, 0, 0.1);
        }

        .payment-option {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .payment-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(153, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .payment-icon {
            transition: all 0.3s ease;
        }

        .payment-option:hover .payment-icon {
            transform: scale(1.1);
        }

        .payment-option:hover .payment-icon i {
            color: var(--primary-color) !important;
            text-shadow: 0 2px 4px rgba(153, 0, 0, 0.3);
        }

        .application-wrapper {
            width: 100%;
            max-width: 100%;
            margin: 0;
            box-sizing: border-box;
        }

        .payment-btn:hover {
            background-color: var(--primary-hover) !important;
            border-color: var(--primary-hover) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(153, 0, 0, 0.3);
        }

        .card-header {
            border-bottom: 3px solid rgba(255, 255, 255, 0.2);
        }

        .text-success {
            color: var(--primary-color) !important;
        }

        .alert-info {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
            color: #333;
        }

        .alert-info .fas {
            color: var(--primary-color);
        }

        .list-unstyled .fas.fa-check {
            color: var(--primary-color) !important;
        }

        .payment-option .card-title {
            color: var(--primary-color);
            font-weight: 600;
        }

        .btn-outline-danger:hover {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(153, 0, 0, 0.2);
        }

        .btn-outline-danger {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-outline-danger .fas {
            margin-right: 5px;
        }

        /* Disabled payment option styles */
        .payment-option-disabled {
            background-color: #f8f9fa !important;
            border-color: #6c757d !important;
            transition: none !important;
        }

        .payment-option-disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        .payment-option-disabled .card-title {
            color: #6c757d !important;
        }

        .payment-option-disabled .card-text {
            color: #6c757d !important;
        }

        .payment-option-disabled .btn:disabled {
            cursor: not-allowed !important;
            opacity: 0.6 !important;
        }
    </style>

    <script>
// Payment status checking variables
let paymentStatusInterval = null;
let paymentCheckStarted = false;
let paymentPopup = null;
const studentPaymentId = <?php echo e($application->student_payment_id ?? 0); ?>;

// Listen for messages from payment popup (if payment gateway supports it)
window.addEventListener('message', function(event) {
    // Only accept messages from trusted payment gateway domains
    // Add your payment gateway domains here
    const trustedDomains = ['************', 'localhost', '127.0.0.1'];
    const origin = new URL(event.origin || 'http://unknown').hostname;

    if (trustedDomains.includes(origin)) {
        console.log('Received message from payment gateway:', event.data);

        // Handle different message types from payment gateway
        if (event.data && typeof event.data === 'object') {
            if (event.data.type === 'payment_success') {
                console.log('Payment success message received from popup');
                // Force an immediate status check
                checkPaymentStatus();
            } else if (event.data.type === 'payment_cancelled') {
                console.log('Payment cancelled message received from popup');
                stopPaymentStatusCheck();
            }
        }
    }
});

// Function to check payment status
function checkPaymentStatus() {
    if (!studentPaymentId || studentPaymentId === 0) {
        console.log('No valid student payment ID found');
        return;
    }

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch('/api/payment/status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            student_payment_id: studentPaymentId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data) {
            const submitStatus = data.data.submit_status;
            const paymentStatus = data.data.application_fee_payment_status;

            console.log('Payment status check:', {
                submit_status: submitStatus,
                payment_status: paymentStatus
            });

            // Check if payment is completed (submit_status = 3)
            if (submitStatus === 3 && paymentStatus === 1) {
                // Payment successful - stop checking and show success message
                stopPaymentStatusCheck();
                showPaymentSuccessMessage(data.data);
            }
        }
    })
    .catch(error => {
        console.error('Error checking payment status:', error);
    });
}

// Function to start payment status checking
function startPaymentStatusCheck() {
    if (paymentCheckStarted) {
        return;
    }

    paymentCheckStarted = true;
    console.log('Starting background payment status check...');

    // Show payment status indicator
    const indicator = document.getElementById('paymentStatusIndicator');
    if (indicator) {
        indicator.style.display = 'block';
    }

    // Check immediately
    checkPaymentStatus();

    // Then check every 5 seconds
    paymentStatusInterval = setInterval(checkPaymentStatus, 5000);

    // Stop checking after 10 minutes (600 seconds)
    setTimeout(() => {
        stopPaymentStatusCheck();
    }, 600000);
}

// Function to stop payment status checking
function stopPaymentStatusCheck() {
    if (paymentStatusInterval) {
        clearInterval(paymentStatusInterval);
        paymentStatusInterval = null;
        paymentCheckStarted = false;
        console.log('Stopped payment status checking');

        // Hide payment status indicator
        const indicator = document.getElementById('paymentStatusIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }

        // Clean up popup reference
        if (paymentPopup && !paymentPopup.closed) {
            try {
                paymentPopup.close();
            } catch (e) {
                console.log('Could not close popup (may be cross-origin):', e.message);
            }
        }
        paymentPopup = null;
    }
}

// Function to show payment success message
function showPaymentSuccessMessage(paymentData) {
    // Update application status badge
    const statusElement = document.getElementById('applicationStatus');
    if (statusElement) {
        statusElement.innerHTML = '<span class="badge badge-pill badge-success">Application Completed with payment</span>';
    }

    // Create success alert
    const successAlert = document.createElement('div');
    successAlert.className = 'alert alert-success alert-dismissible fade show';
    successAlert.style.position = 'fixed';
    successAlert.style.top = '20px';
    successAlert.style.right = '20px';
    successAlert.style.zIndex = '9999';
    successAlert.style.minWidth = '300px';
    successAlert.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <strong>Payment Successful!</strong><br>
        Your payment has been processed successfully.<br>
        <small>Amount: ${paymentData.paid_amount ? 'Rs. ' + paymentData.paid_amount : 'N/A'}</small>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Add to page
    document.body.appendChild(successAlert);

    // Auto remove after 10 seconds
    setTimeout(() => {
        if (successAlert.parentNode) {
            successAlert.parentNode.removeChild(successAlert);
        }
    }, 10000);

    // Optionally redirect to success page after a delay
    setTimeout(() => {
        if (confirm('Payment completed successfully! Would you like to view your application?')) {
            // You can redirect to application view page here
            window.location.reload();
        }
    }, 3000);
}

document.getElementById('cardPaymentForm').addEventListener('submit', function (e) {
    e.preventDefault();

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Show loading indicator
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    submitBtn.disabled = true;

    // Create FormData from the form
    const formData = new FormData(this);

    fetch('<?php echo e(route('card.payment.process')); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': csrfToken
        },
        body: formData
    })
    .then(response => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text();
    })
    .then(html => {
        console.log('Card payment response:', html.substring(0, 200));

        // Start background payment status checking
        startPaymentStatusCheck();

        console.log('Payment popup opened. Note: Cross-origin errors when accessing popup are normal when redirected to payment gateway.');

        // Open popup window for payment gateway
        paymentPopup = window.open('', 'PaymentPopup', 'width=800,height=600,scrollbars=yes,resizable=yes');

        if (paymentPopup) {
            try {
                paymentPopup.document.open();
                paymentPopup.document.write(html);
                paymentPopup.document.close();
            } catch (popupError) {
                console.log('Could not write to popup (may be cross-origin):', popupError.message);
                // This is normal if the popup immediately redirects to payment gateway
            }

            // Monitor popup close event with cross-origin safety
            const checkClosed = setInterval(() => {
                try {
                    // Try to access popup properties safely
                    if (paymentPopup.closed) {
                        clearInterval(checkClosed);
                        console.log('Payment popup closed');
                        paymentPopup = null;
                        // Continue checking payment status even after popup closes
                        // Don't stop the status checking as payment might still be processing
                    }
                } catch (e) {
                    // Cross-origin error - popup is likely on payment gateway domain
                    // This is expected behavior, just continue monitoring
                    console.log('Popup is on external domain (payment gateway) - this is normal');

                    // Check if popup still exists but we can't access it
                    try {
                        if (paymentPopup.closed) {
                            clearInterval(checkClosed);
                            console.log('Payment popup closed after cross-origin navigation');
                            paymentPopup = null;
                        }
                    } catch (innerError) {
                        // If we can't even check if it's closed, assume it's still open
                        // The payment status checking will handle completion detection
                        console.log('Cannot access popup due to cross-origin restrictions - continuing status monitoring');
                    }
                }
            }, 1000);

            // Also add a fallback timeout for popup monitoring
            setTimeout(() => {
                clearInterval(checkClosed);
                console.log('Stopped popup monitoring after 15 minutes');
            }, 900000); // 15 minutes

        } else {
            alert('Popup blocked! Please allow popups for this site to complete payment.');
        }
    })
    .catch(err => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        console.error('Payment error:', err);
        alert('Something went wrong. Please try again. Error: ' + err.message);
    });
});

// Optional: Start checking on page load if payment is already in progress
document.addEventListener('DOMContentLoaded', function() {
    // Check current status on page load
    if (studentPaymentId && studentPaymentId !== 0) {
        checkPaymentStatus();
    }
});

</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/frontend/payment/index.blade.php ENDPATH**/ ?>