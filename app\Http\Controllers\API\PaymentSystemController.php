<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Applicant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class PaymentSystemController extends Controller
{
    public function updatePayment(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'student_payment_id' => 'required|numeric|not_in:0|exists:applicants,student_payment_id',
            'paid_amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',


        ],[
            'student_payment_id.exists' => 'The student payment ID does not exist',
            'student_payment_id.not_in' => 'The student payment ID is invalid',
            'student_payment_id.numeric' => 'The student payment ID must be a number',
            'student_payment_id.required' => 'The student payment ID is required',
            'paid_amount.required' => 'The paid amount is required',
            'paid_amount.numeric' => 'The paid amount must be a number',
            'paid_amount.min' => 'The paid amount must be greater than or equal to 0',
            'payment_date.required' => 'The payment date is required',
            'payment_date.date' => 'The payment date must be a valid date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {

            $applicant = Applicant::where('student_payment_id',$request->student_payment_id)->first();

            if (!$applicant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Applicant not found'
                ], 404);
            }

            // Update payment information
            $applicant->application_fee_payment_status = 1;
            $applicant->application_fee_payment_date = $request->payment_date;
            $applicant->paid_amount = $request->paid_amount;
            $applicant->payment_method = 2;
            $applicant->submit_status = 3;
            $applicant->final_submit_date = Carbon::now();
            $applicant->save();

            return response()->json([
                'success' => true,
                'message' => 'Payment information updated successfully',
                'data' => $applicant->student_payment_id
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment information',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function checkPaymentStatus(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'student_payment_id' => 'required|numeric|not_in:0|exists:applicants,student_payment_id',
        ],[
            'student_payment_id.exists' => 'The student payment ID does not exist',
            'student_payment_id.not_in' => 'The student payment ID is invalid',
            'student_payment_id.numeric' => 'The student payment ID must be a number',
            'student_payment_id.required' => 'The student payment ID is required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $applicant = Applicant::where('student_payment_id', $request->student_payment_id)->first();

            if (!$applicant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Applicant not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'student_payment_id' => $applicant->student_payment_id,
                    'submit_status' => $applicant->submit_status,
                    'application_fee_payment_status' => $applicant->application_fee_payment_status,
                    'payment_method' => $applicant->payment_method ?? null,
                    'paid_amount' => $applicant->paid_amount ?? null,
                    'application_fee_payment_date' => $applicant->application_fee_payment_date,
                    'final_submit_date' => $applicant->final_submit_date,
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check payment status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
