<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\PaymentSystemController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Payment System API Routes
Route::post('/payment/update', [PaymentSystemController::class, 'updatePayment'])->middleware('api-key');
Route::post('/payment/status', [PaymentSystemController::class, 'checkPaymentStatus']);
